/* 发布页面增强样式 - 数字鱼虚拟商品交易平台 */

/* 发布页面容器增强 */
.publish-container {
    background: linear-gradient(135deg, var(--neutral-100), var(--neutral-200));
    border-radius: 16px;
    padding: 35px;
    box-shadow: var(--shadow-md), 0 10px 30px rgba(255, 111, 6, 0.05);
    max-width: 900px;
    margin: 20px auto 50px;
    position: relative;
    overflow: hidden;
}

.publish-container::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 150px;
    height: 150px;
    background: var(--primary-gradient);
    opacity: 0.05;
    border-radius: 0 0 0 100%;
    z-index: 0;
}

.page-title {
    font-size: 28px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 35px;
    padding-bottom: 18px;
    border-bottom: 2px solid var(--border-light);
    position: relative;
}

.page-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 80px;
    height: 2px;
    background: var(--primary-gradient);
}

/* 表单组增强 */
.publish-form .form-group {
    margin-bottom: 30px;
    position: relative;
    transition: all 0.3s ease;
}

.publish-form .form-group:hover {
    transform: translateY(-2px);
}

.form-label {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 12px;
    position: relative;
    padding-left: 12px;
}

.form-label::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 16px;
    background: var(--primary-gradient);
    border-radius: 2px;
}

/* 输入框增强 */
.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: 14px 16px;
    border: 1px solid var(--border-light);
    border-radius: 10px;
    font-size: 15px;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
    background-color: var(--neutral-100);
}

.form-input:hover,
.form-select:hover,
.form-textarea:hover {
    border-color: var(--primary-light);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 111, 6, 0.15);
    outline: none;
}

.input-tips {
    font-size: 13px;
    color: var(--text-muted);
    margin-top: 8px;
    padding-left: 5px;
    transition: all 0.3s ease;
}

.form-group:hover .input-tips {
    color: var(--text-secondary);
}

/* 商品类型选择器增强 */
.product-type-selector {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
}

.type-option {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 18px 15px;
    border: 2px solid var(--border-light);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: var(--neutral-200);
    position: relative;
    overflow: hidden;
}

.type-option:hover {
    background-color: var(--bg-feature);
    border-color: var(--primary-light);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.type-option input[type="radio"] {
    margin-right: 10px;
    accent-color: var(--primary-color);
    transform: scale(1.3);
    transition: all 0.3s ease;
}

.type-option input[type="radio"]:checked + label {
    color: var(--primary-color);
    font-weight: 600;
}

.type-option:has(input[type="radio"]:checked) {
    border-color: var(--primary-color);
    background-color: var(--bg-feature);
    box-shadow: 0 5px 15px rgba(255, 111, 6, 0.1);
}

.type-option label {
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s ease;
    z-index: 1;
}

/* 虚拟商品分类选择器增强 */
.virtual-category-selector {
    max-width: 450px;
    position: relative;
}

.virtual-category-selector::after {
    content: '\25BC';
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--primary-color);
    pointer-events: none;
    font-size: 12px;
}

.form-select {
    appearance: none;
    padding-right: 30px;
    cursor: pointer;
}

/* 图片上传增强 */
.image-uploader {
    margin-bottom: 20px;
}

.upload-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 15px;
}

.upload-item {
    width: 140px;
    height: 140px;
    border-radius: 12px;
    overflow: hidden;
    position: relative;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
}

.upload-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.add-image {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border: 2px dashed var(--border-light);
    background-color: var(--neutral-200);
    cursor: pointer;
    transition: all 0.3s ease;
}

.add-image:hover {
    border-color: var(--primary-color);
    background-color: var(--bg-feature);
}

.add-image i {
    font-size: 32px;
    color: var(--primary-color);
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.add-image:hover i {
    transform: scale(1.2);
}

.add-image span {
    font-size: 14px;
    color: var(--text-secondary);
    font-weight: 500;
}

.upload-tips {
    background-color: var(--neutral-200);
    padding: 12px 15px;
    border-radius: 8px;
    border-left: 3px solid var(--primary-color);
}

.upload-tips p {
    font-size: 13px;
    color: var(--text-secondary);
    margin-bottom: 5px;
}

.upload-tips p:last-child {
    margin-bottom: 0;
}

/* 虚拟商品属性增强 */
.virtual-attributes {
    background-color: var(--neutral-200);
    border-radius: 12px;
    padding: 20px;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-light);
}

.attribute-item {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.attribute-item:last-child {
    margin-bottom: 0;
}

.attribute-label {
    width: 100px;
    font-size: 15px;
    color: var(--text-secondary);
    font-weight: 500;
}

.attribute-item .form-select,
.attribute-item .form-input {
    flex: 1;
    min-width: 200px;
}

.period-input {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 12px;
    margin-left: 100px;
}

.period-input .form-input {
    width: 120px;
}

.period-input .form-select {
    width: 100px;
}

/* 价格输入增强 */
.price-input {
    display: flex;
    align-items: center;
    max-width: 300px;
    position: relative;
}

.price-symbol {
    position: absolute;
    left: 15px;
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-color);
}

.price-input .form-input {
    padding-left: 35px;
    font-size: 16px;
    font-weight: 500;
}

/* 交易方式增强 */
.trade-options,
.service-options {
    display: flex;
    flex-wrap: wrap;
    gap: 25px;
    margin-bottom: 15px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 10px 15px;
    background-color: var(--neutral-200);
    border-radius: 8px;
    transition: all 0.3s ease;
    border: 1px solid var(--border-light);
}

.checkbox-label:hover {
    background-color: var(--bg-feature);
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

.checkbox-label input[type="checkbox"] {
    margin-right: 10px;
    accent-color: var(--primary-color);
    transform: scale(1.2);
}

.checkbox-label:has(input[type="checkbox"]:checked) {
    background-color: var(--bg-feature);
    border-color: var(--primary-light);
    box-shadow: 0 3px 10px rgba(255, 111, 6, 0.1);
}

/* 内容选项卡增强 */
.content-tabs {
    display: flex;
    border-bottom: 2px solid var(--border-light);
    margin-bottom: 20px;
}

.content-tab {
    padding: 12px 25px;
    cursor: pointer;
    font-size: 15px;
    font-weight: 500;
    color: var(--text-secondary);
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
    margin-bottom: -2px;
}

.content-tab:hover {
    color: var(--primary-light);
}

.content-tab.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    font-weight: 600;
}

/* 文件上传增强 */
.file-uploader {
    margin-bottom: 20px;
}

.upload-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 25px;
    background-color: var(--neutral-200);
    border: 1px solid var(--border-light);
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.upload-btn:hover {
    background-color: var(--bg-feature);
    border-color: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.upload-btn i {
    font-size: 20px;
    margin-right: 10px;
    color: var(--primary-color);
}

.file-list {
    margin-top: 15px;
}

.file-item {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    background-color: var(--neutral-200);
    border-radius: 8px;
    margin-bottom: 10px;
    border: 1px solid var(--border-light);
    transition: all 0.3s ease;
}

.file-item:hover {
    background-color: var(--bg-feature);
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

.file-icon {
    font-size: 22px;
    color: var(--primary-color);
    margin-right: 12px;
}

.file-info {
    flex: 1;
}

.file-name {
    font-size: 14px;
    color: var(--text-primary);
    margin-bottom: 3px;
    font-weight: 500;
}

.file-size {
    font-size: 12px;
    color: var(--text-muted);
}

.file-remove {
    color: var(--primary-color);
    cursor: pointer;
    font-size: 18px;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.file-remove:hover {
    background-color: rgba(255, 111, 6, 0.1);
    transform: scale(1.1);
}

/* 表单操作增强 */
.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 20px;
    margin-top: 40px;
    padding-top: 25px;
    border-top: 1px solid var(--border-light);
}

.btn {
    padding: 14px 30px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.btn-primary {
    background: var(--primary-gradient);
    border: none;
    color: white;
    box-shadow: 0 5px 15px rgba(255, 111, 6, 0.2);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(255, 111, 6, 0.3);
}

.btn-outline {
    border: 1px solid var(--border-medium);
    background-color: transparent;
    color: var(--text-secondary);
}

.btn-outline:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .publish-container {
        padding: 25px 20px;
        margin: 15px 10px 30px;
        border-radius: 12px;
    }
    
    .page-title {
        font-size: 22px;
        margin-bottom: 25px;
        padding-bottom: 15px;
    }
    
    .form-label {
        font-size: 15px;
    }
    
    .attribute-label {
        width: 100%;
        margin-bottom: 8px;
    }
    
    .period-input {
        margin-left: 0;
        width: 100%;
    }
    
    .type-option {
        padding: 12px 10px;
    }
    
    .type-option label {
        font-size: 14px;
    }
    
    .upload-item {
        width: 120px;
        height: 120px;
    }
    
    .trade-options,
    .service-options {
        gap: 10px;
    }
    
    .checkbox-label {
        padding: 8px 12px;
        font-size: 14px;
    }
    
    .content-tab {
        padding: 10px 15px;
        font-size: 14px;
    }
    
    .form-actions {
        flex-direction: column;
        margin-top: 30px;
    }
    
    .btn {
        width: 100%;
        padding: 12px 20px;
    }
}

/* 深色模式调整 */
@media (prefers-color-scheme: dark) {
    .publish-container::before {
        opacity: 0.1;
    }
    
    .upload-tips {
        background-color: var(--bg-feature);
    }
    
    .checkbox-label:has(input[type="checkbox"]:checked) {
        background-color: rgba(255, 111, 6, 0.15);
    }
    
    .type-option:has(input[type="radio"]:checked) {
        background-color: rgba(255, 111, 6, 0.15);
    }
}