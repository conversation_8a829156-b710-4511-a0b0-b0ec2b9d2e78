/* 发布页面增强样式 - 数字鱼虚拟商品交易平台 */

/* CSS变量定义 */
:root {
    --primary-color: #ff6f06;
    --primary-light: #ff8533;
    --primary-gradient: linear-gradient(135deg, #ff6f06, #ff8533);
    --neutral-100: #ffffff;
    --neutral-200: #f8f9fa;
    --neutral-300: #e9ecef;
    --border-light: #dee2e6;
    --border-medium: #ced4da;
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --text-muted: #adb5bd;
    --bg-feature: #fff2e8;
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
}

/* 发布页面容器增强 */
.publish-container {
    background: linear-gradient(135deg, var(--neutral-100), var(--neutral-200));
    border-radius: 20px;
    padding: 40px;
    box-shadow: var(--shadow-md), 0 15px 35px rgba(255, 111, 6, 0.08);
    max-width: 950px;
    margin: 30px auto 60px;
    position: relative;
    overflow: hidden;
}

.publish-container::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 200px;
    height: 200px;
    background: var(--primary-gradient);
    opacity: 0.03;
    border-radius: 0 0 0 100%;
    z-index: 0;
}

/* 发布提示区域 */
.publish-notice {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    background: linear-gradient(135deg, var(--bg-feature), #fff5f0);
    border: 1px solid var(--primary-light);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 30px;
    position: relative;
}

.notice-icon {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
}

.notice-content h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.notice-content p {
    margin: 0;
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.5;
}

.page-title {
    font-size: 32px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 40px;
    padding-bottom: 20px;
    border-bottom: 3px solid var(--border-light);
    position: relative;
    z-index: 1;
}

.page-title::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 100px;
    height: 3px;
    background: var(--primary-gradient);
    border-radius: 2px;
}

/* 表单组增强 */
.publish-form .form-group {
    margin-bottom: 30px;
    position: relative;
    transition: all 0.3s ease;
}

.publish-form .form-group:hover {
    transform: translateY(-2px);
}

.form-label {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 12px;
    position: relative;
    padding-left: 12px;
}

.form-label::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 16px;
    background: var(--primary-gradient);
    border-radius: 2px;
}

/* 输入框增强 */
.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: 14px 16px;
    border: 1px solid var(--border-light);
    border-radius: 10px;
    font-size: 15px;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
    background-color: var(--neutral-100);
}

.form-input:hover,
.form-select:hover,
.form-textarea:hover {
    border-color: var(--primary-light);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 111, 6, 0.15);
    outline: none;
}

.input-tips {
    font-size: 13px;
    color: var(--text-muted);
    margin-top: 8px;
    padding-left: 5px;
    transition: all 0.3s ease;
}

.form-group:hover .input-tips {
    color: var(--text-secondary);
}

/* 商品类型选择器增强 */
.product-type-selector {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
}

.type-option {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 18px 15px;
    border: 2px solid var(--border-light);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: var(--neutral-200);
    position: relative;
    overflow: hidden;
}

.type-option:hover {
    background-color: var(--bg-feature);
    border-color: var(--primary-light);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.type-option input[type="radio"] {
    margin-right: 10px;
    accent-color: var(--primary-color);
    transform: scale(1.3);
    transition: all 0.3s ease;
}

.type-option input[type="radio"]:checked + label {
    color: var(--primary-color);
    font-weight: 600;
}

.type-option:has(input[type="radio"]:checked) {
    border-color: var(--primary-color);
    background-color: var(--bg-feature);
    box-shadow: 0 5px 15px rgba(255, 111, 6, 0.1);
}

.type-option label {
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s ease;
    z-index: 1;
}

/* 虚拟商品分类选择器增强 */
.virtual-category-selector {
    max-width: 450px;
    position: relative;
}

.virtual-category-selector::after {
    content: '\25BC';
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--primary-color);
    pointer-events: none;
    font-size: 12px;
}

.form-select {
    appearance: none;
    padding-right: 30px;
    cursor: pointer;
}

/* 图片上传增强 */
.image-uploader {
    margin-bottom: 25px;
}

.upload-area {
    border: 2px dashed var(--border-medium);
    border-radius: 16px;
    padding: 20px;
    background-color: var(--neutral-200);
    transition: all 0.3s ease;
    position: relative;
}

.upload-area.drag-over {
    border-color: var(--primary-color);
    background-color: var(--bg-feature);
    transform: scale(1.02);
}

.upload-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    min-height: 160px;
    align-items: flex-start;
}

.upload-item {
    width: 160px;
    height: 160px;
    border-radius: 16px;
    overflow: hidden;
    position: relative;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
}

.upload-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.15);
}

.add-image {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border: 2px dashed var(--primary-light);
    background: linear-gradient(135deg, var(--neutral-100), var(--bg-feature));
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    height: 100%;
}

.add-image:hover {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, var(--bg-feature), #fff5f0);
    transform: scale(1.05);
}

.upload-icon {
    margin-bottom: 12px;
}

.upload-icon i {
    font-size: 36px;
    color: var(--primary-color);
    transition: all 0.3s ease;
}

.add-image:hover .upload-icon i {
    transform: scale(1.2) rotate(5deg);
}

.upload-text {
    text-align: center;
}

.upload-title {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.upload-subtitle {
    display: block;
    font-size: 13px;
    color: var(--text-secondary);
}

/* 图片预览样式 */
.image-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.preview-overlay {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.image-preview:hover .preview-overlay {
    opacity: 1;
}

.remove-btn {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    color: var(--danger-color);
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.remove-btn:hover {
    background: var(--danger-color);
    color: white;
    transform: scale(1.1);
}

.upload-tips {
    background: linear-gradient(135deg, var(--neutral-200), var(--neutral-300));
    padding: 18px 20px;
    border-radius: 12px;
    border-left: 4px solid var(--primary-color);
    margin-top: 20px;
}

.tip-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
}

.tip-item:last-child {
    margin-bottom: 0;
}

.tip-item i {
    color: var(--primary-color);
    font-size: 14px;
    flex-shrink: 0;
}

.tip-item span {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.4;
}

/* 虚拟商品属性增强 */
.virtual-attributes {
    background: linear-gradient(135deg, var(--neutral-200), var(--neutral-300));
    border-radius: 16px;
    padding: 25px;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-light);
}

.attribute-item {
    margin-bottom: 25px;
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 15px;
}

.attribute-item:last-child {
    margin-bottom: 0;
}

.attribute-label {
    min-width: 120px;
    font-size: 15px;
    color: var(--text-primary);
    font-weight: 600;
    padding-top: 12px;
}

.attribute-content {
    flex: 1;
    min-width: 250px;
}

.attribute-content .form-select,
.attribute-content .form-input {
    width: 100%;
}

.period-input {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-top: 15px;
    padding: 15px;
    background-color: var(--neutral-100);
    border-radius: 10px;
    border: 1px solid var(--border-light);
}

.period-input .form-input {
    width: 120px;
}

.period-input .form-select {
    width: 100px;
}

/* 价格输入增强 */
.price-input {
    display: flex;
    align-items: center;
    max-width: 300px;
    position: relative;
}

.price-symbol {
    position: absolute;
    left: 15px;
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-color);
}

.price-input .form-input {
    padding-left: 35px;
    font-size: 16px;
    font-weight: 500;
}

/* 售后服务选项增强 */
.service-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    cursor: pointer;
    padding: 20px;
    background: linear-gradient(135deg, var(--neutral-100), var(--neutral-200));
    border-radius: 12px;
    transition: all 0.3s ease;
    border: 2px solid var(--border-light);
    position: relative;
    overflow: hidden;
}

.checkbox-label::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--primary-gradient);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.checkbox-label:hover::before,
.checkbox-label:has(input[type="checkbox"]:checked)::before {
    transform: scaleY(1);
}

.checkbox-label:hover {
    background: linear-gradient(135deg, var(--bg-feature), #fff5f0);
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-medium);
    border-radius: 4px;
    margin-right: 15px;
    margin-top: 2px;
    flex-shrink: 0;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-label:has(input[type="checkbox"]:checked) .checkmark {
    background: var(--primary-gradient);
    border-color: var(--primary-color);
}

.checkbox-label:has(input[type="checkbox"]:checked) .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.service-text {
    flex: 1;
}

.service-text strong {
    display: block;
    font-size: 15px;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.service-text small {
    font-size: 13px;
    color: var(--text-secondary);
    line-height: 1.4;
}

.checkbox-label:has(input[type="checkbox"]:checked) {
    background: linear-gradient(135deg, var(--bg-feature), #fff2e8);
    border-color: var(--primary-light);
    box-shadow: 0 5px 15px rgba(255, 111, 6, 0.15);
}

/* 内容选项卡增强 */
.content-tabs {
    display: flex;
    background-color: var(--neutral-200);
    border-radius: 12px;
    padding: 6px;
    margin-bottom: 25px;
    box-shadow: var(--shadow-sm);
}

.content-tab {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 15px 20px;
    cursor: pointer;
    font-size: 15px;
    font-weight: 500;
    color: var(--text-secondary);
    border-radius: 8px;
    transition: all 0.3s ease;
    background-color: transparent;
}

.content-tab:hover {
    color: var(--primary-light);
    background-color: rgba(255, 111, 6, 0.1);
}

.content-tab.active {
    color: var(--primary-color);
    background: linear-gradient(135deg, var(--neutral-100), var(--bg-feature));
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.content-tab i {
    font-size: 16px;
}

.content-panel {
    display: none;
    animation: fadeIn 0.3s ease-out;
}

.content-panel.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 文件上传增强 */
.file-uploader {
    margin-bottom: 20px;
}

.upload-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 25px;
    background-color: var(--neutral-200);
    border: 1px solid var(--border-light);
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.upload-btn:hover {
    background-color: var(--bg-feature);
    border-color: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.upload-btn i {
    font-size: 20px;
    margin-right: 10px;
    color: var(--primary-color);
}

.file-list {
    margin-top: 15px;
}

.file-item {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    background-color: var(--neutral-200);
    border-radius: 8px;
    margin-bottom: 10px;
    border: 1px solid var(--border-light);
    transition: all 0.3s ease;
}

.file-item:hover {
    background-color: var(--bg-feature);
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

.file-icon {
    font-size: 22px;
    color: var(--primary-color);
    margin-right: 12px;
}

.file-info {
    flex: 1;
}

.file-name {
    font-size: 14px;
    color: var(--text-primary);
    margin-bottom: 3px;
    font-weight: 500;
}

.file-size {
    font-size: 12px;
    color: var(--text-muted);
}

.file-remove {
    color: var(--primary-color);
    cursor: pointer;
    font-size: 18px;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.file-remove:hover {
    background-color: rgba(255, 111, 6, 0.1);
    transform: scale(1.1);
}

/* 表单操作增强 */
.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 20px;
    margin-top: 50px;
    padding-top: 30px;
    border-top: 2px solid var(--border-light);
}

.btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px 32px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 12px;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    text-decoration: none;
    min-width: 140px;
    justify-content: center;
}

.btn-primary {
    background: var(--primary-gradient);
    color: white;
    box-shadow: 0 6px 20px rgba(255, 111, 6, 0.25);
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px rgba(255, 111, 6, 0.35);
}

.btn-primary:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.btn-outline {
    border: 2px solid var(--border-medium);
    background-color: transparent;
    color: var(--text-secondary);
}

.btn-outline:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background-color: var(--bg-feature);
    transform: translateY(-4px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.btn i {
    font-size: 16px;
}

/* 动画效果 */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeOut {
    from { opacity: 1; transform: translateY(0); }
    to { opacity: 0; transform: translateY(-10px); }
}

@keyframes scaleIn {
    from { opacity: 0; transform: scale(0.9); }
    to { opacity: 1; transform: scale(1); }
}

.animate-fade-in {
    animation: fadeIn 0.3s ease-out forwards;
}

.animate-fade-out {
    animation: fadeOut 0.3s ease-out forwards;
}

.animate-scale-in {
    animation: scaleIn 0.3s ease-out forwards;
}

.spin {
    animation: spin 1s linear infinite;
}

/* 错误提示样式 */
.input-error {
    border-color: var(--danger-color) !important;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.15) !important;
}

.error-message {
    display: flex;
    align-items: center;
    gap: 6px;
    color: var(--danger-color);
    font-size: 13px;
    margin-top: 8px;
    animation: fadeIn 0.3s ease-out;
}

.error-message i {
    font-size: 14px;
}

/* 消息提示样式 */
.message-toast {
    position: fixed;
    top: 30px;
    right: 30px;
    padding: 18px 24px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: 12px;
    z-index: 1000;
    transform: translateX(400px);
    opacity: 0;
    transition: all 0.3s ease;
    max-width: 350px;
    border-left: 4px solid var(--info-color);
}

.message-toast.show {
    transform: translateX(0);
    opacity: 1;
}

.message-toast.success {
    border-left-color: var(--success-color);
}

.message-toast.error {
    border-left-color: var(--danger-color);
}

.message-toast.warning {
    border-left-color: var(--warning-color);
}

.message-icon {
    font-size: 20px;
    flex-shrink: 0;
}

.message-toast.success .message-icon {
    color: var(--success-color);
}

.message-toast.error .message-icon {
    color: var(--danger-color);
}

.message-toast.warning .message-icon {
    color: var(--warning-color);
}

.message-toast.info .message-icon {
    color: var(--info-color);
}

.message-content {
    font-size: 14px;
    color: var(--text-primary);
    line-height: 1.4;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .publish-container {
        padding: 25px 15px;
        margin: 15px 10px 40px;
        border-radius: 16px;
    }

    .publish-container::before {
        width: 120px;
        height: 120px;
    }

    .page-title {
        font-size: 24px;
        margin-bottom: 30px;
        padding-bottom: 15px;
    }

    .publish-notice {
        flex-direction: column;
        text-align: center;
        padding: 20px 15px;
    }

    .notice-icon {
        align-self: center;
    }

    .form-label {
        font-size: 15px;
        margin-bottom: 10px;
    }

    /* 属性项移动端布局 */
    .attribute-item {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .attribute-label {
        min-width: auto;
        padding-top: 0;
        font-size: 14px;
    }

    .attribute-content {
        min-width: auto;
    }

    .period-input {
        margin-top: 10px;
        flex-wrap: wrap;
        gap: 10px;
    }

    .period-input .form-input,
    .period-input .form-select {
        flex: 1;
        min-width: 100px;
    }

    /* 图片上传移动端优化 */
    .upload-area {
        padding: 15px;
    }

    .upload-preview {
        gap: 15px;
        justify-content: center;
    }

    .upload-item {
        width: 140px;
        height: 140px;
    }

    .upload-title {
        font-size: 14px;
    }

    .upload-subtitle {
        font-size: 12px;
    }

    /* 服务选项移动端布局 */
    .service-options {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .checkbox-label {
        padding: 15px;
    }

    .service-text strong {
        font-size: 14px;
    }

    .service-text small {
        font-size: 12px;
    }

    /* 内容选项卡移动端 */
    .content-tabs {
        padding: 4px;
    }

    .content-tab {
        padding: 12px 15px;
        font-size: 14px;
    }

    .content-tab span {
        display: none;
    }

    .content-tab i {
        font-size: 18px;
    }

    /* 按钮移动端布局 */
    .form-actions {
        flex-direction: column;
        margin-top: 40px;
        gap: 15px;
    }

    .btn {
        width: 100%;
        padding: 14px 20px;
        min-width: auto;
    }

    /* 消息提示移动端 */
    .message-toast {
        top: 20px;
        right: 15px;
        left: 15px;
        max-width: none;
        transform: translateY(-100px);
    }

    .message-toast.show {
        transform: translateY(0);
    }
}

/* 小屏幕设备进一步优化 */
@media (max-width: 480px) {
    .publish-container {
        padding: 20px 12px;
        margin: 10px 5px 30px;
    }

    .page-title {
        font-size: 20px;
        margin-bottom: 25px;
    }

    .upload-item {
        width: 120px;
        height: 120px;
    }

    .upload-preview {
        gap: 12px;
    }

    .virtual-attributes {
        padding: 20px 15px;
    }

    .tip-item {
        font-size: 13px;
    }

    .btn {
        padding: 12px 16px;
        font-size: 15px;
    }
}

/* 深色模式调整 */
@media (prefers-color-scheme: dark) {
    .publish-container::before {
        opacity: 0.1;
    }
    
    .upload-tips {
        background-color: var(--bg-feature);
    }
    
    .checkbox-label:has(input[type="checkbox"]:checked) {
        background-color: rgba(255, 111, 6, 0.15);
    }
    
    .type-option:has(input[type="radio"]:checked) {
        background-color: rgba(255, 111, 6, 0.15);
    }
}