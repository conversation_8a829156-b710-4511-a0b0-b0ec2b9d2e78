/* 增强样式 - 美化数字鱼虚拟商品交易平台 */

/* 用户信息区域增强 */
.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

/* 通知图标 */
.notification-icon {
    position: relative;
}

.notification-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: #f8f9fa;
    border-radius: 50%;
    color: #6c757d;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

.notification-btn:hover {
    background-color: #e9ecef;
    color: #495057;
    transform: scale(1.05);
}

.notification-btn i {
    font-size: 18px;
}

.notification-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    background: linear-gradient(135deg, #ff6f06, #ff8533);
    color: white;
    font-size: 11px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(255, 111, 6, 0.3);
}

/* 用户下拉菜单 */
.user-dropdown {
    position: relative;
}

.user-trigger {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background-color: #f8f9fa;
    border-radius: 25px;
    text-decoration: none;
    color: #495057;
    transition: all 0.3s ease;
    cursor: pointer;
    border: 2px solid transparent;
}

.user-trigger:hover,
.user-trigger.active {
    background-color: #fff2e8;
    border-color: #ff6f06;
    color: #ff6f06;
}

.user-avatar-img {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.user-name {
    font-size: 14px;
    font-weight: 500;
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.dropdown-arrow {
    font-size: 12px;
    transition: transform 0.3s ease;
}

.user-trigger.active .dropdown-arrow {
    transform: rotate(180deg);
}

/* 下拉菜单 */
.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    border: 1px solid #e9ecef;
    min-width: 280px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    margin-top: 8px;
}

.dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-header {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 20px;
    border-bottom: 1px solid #f1f3f4;
}

.dropdown-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dropdown-user-info {
    flex: 1;
}

.dropdown-username {
    font-size: 16px;
    font-weight: 600;
    color: #212529;
    margin-bottom: 4px;
}

.dropdown-user-level {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 13px;
    color: #6c757d;
}

.dropdown-user-level i {
    color: #ffc107;
    font-size: 12px;
}

.dropdown-divider {
    height: 1px;
    background-color: #f1f3f4;
    margin: 8px 0;
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    color: #495057;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 14px;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
    color: #ff6f06;
}

.dropdown-item i {
    width: 18px;
    font-size: 16px;
    text-align: center;
}

.logout-item {
    color: #dc3545;
}

.logout-item:hover {
    background-color: #fff5f5;
    color: #dc3545;
}

/* 登录按钮 */
.login-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    background: linear-gradient(135deg, #ff6f06, #ff8533);
    color: white;
    text-decoration: none;
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(255, 111, 6, 0.3);
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 111, 6, 0.4);
    color: white;
}

.login-btn i {
    font-size: 18px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .user-info {
        gap: 10px;
    }

    .notification-btn {
        width: 36px;
        height: 36px;
    }

    .notification-btn i {
        font-size: 16px;
    }

    .user-trigger {
        padding: 6px 10px;
    }

    .user-avatar-img {
        width: 28px;
        height: 28px;
    }

    .user-name {
        font-size: 13px;
        max-width: 80px;
    }

    .dropdown-menu {
        min-width: 260px;
        right: -10px;
    }

    .dropdown-header {
        padding: 16px;
    }

    .dropdown-avatar {
        width: 40px;
        height: 40px;
    }

    .dropdown-username {
        font-size: 15px;
    }

    .dropdown-item {
        padding: 10px 16px;
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .user-info {
        gap: 8px;
    }

    .notification-btn {
        width: 32px;
        height: 32px;
    }

    .notification-btn i {
        font-size: 14px;
    }

    .notification-badge {
        font-size: 10px;
        min-width: 16px;
        height: 16px;
        padding: 1px 4px;
    }

    .user-name {
        display: none;
    }

    .dropdown-menu {
        min-width: 240px;
        right: -20px;
    }

    .login-btn {
        padding: 8px 16px;
        font-size: 14px;
    }

    .login-btn span {
        display: none;
    }

    .login-btn i {
        font-size: 20px;
    }
}

/* 全局样式增强 */
body {
    font-family: 'Noto Sans SC', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* 按钮样式增强 */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
}

.btn-primary {
    background: linear-gradient(135deg, #ff7e1a, #ff6f06);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #ff6f06, #ff5500);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(255, 111, 6, 0.3);
}

.btn-outline {
    border: 1px solid #e0e0e0;
    background-color: #fff;
}

.btn-outline:hover {
    border-color: #ff6f06;
    color: #ff6f06;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

/* 头部导航增强 */
.header {
    background-color: #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.logo-text {
    font-weight: 600;
    background: linear-gradient(135deg, #ff7e1a, #ff5500);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-left: 8px;
    font-size: 22px;
}

.search-box input {
    border-radius: 8px;
    padding: 0 45px 0 15px;
    height: 42px;
    border: 1px solid #e5e5e5;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
    transition: all 0.3s ease;
}

.search-box input:focus {
    border-color: #ff6f06;
    box-shadow: 0 0 0 3px rgba(255, 111, 6, 0.1);
}

.search-btn {
    color: #ff6f06;
}

.nav a {
    font-weight: 500;
    position: relative;
    padding: 8px 0;
}

.nav a:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: #ff6f06;
    transition: width 0.3s ease;
}

.nav a:hover:after {
    width: 100%;
}

.nav a.active:after {
    width: 100%;
}

.publish-btn {
    color: #ff6f06;
    font-weight: 600;
}

/* 商品卡片增强 */
.product-card {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.product-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.product-img {
    overflow: hidden;
}

.product-img img {
    transition: transform 0.5s ease;
}

.product-card:hover .product-img img {
    transform: scale(1.08);
}

.product-tag {
    background: linear-gradient(135deg, #ff7e1a, #ff5500);
    padding: 4px 10px;
    border-radius: 4px;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(255, 111, 6, 0.3);
}

.product-info {
    padding: 15px;
}

.product-title {
    font-size: 15px;
    font-weight: 500;
    margin-bottom: 10px;
    line-height: 1.4;
    height: 42px;
}

.product-price {
    margin-bottom: 10px;
}

.current-price {
    font-size: 18px;
    font-weight: 700;
    color: #ff5500;
}

.original-price {
    font-size: 13px;
    color: #999;
    text-decoration: line-through;
    margin-left: 5px;
}

.virtual-attrs {
    margin: 10px 0;
}

.attr-item {
    font-size: 12px;
    padding: 3px 10px;
    border-radius: 4px;
    background-color: #f0f0f0;
    transition: all 0.3s ease;
}

.attr-item:hover {
    background-color: #ffe8d9;
}

.product-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #f0f0f0;
}

.seller {
    display: flex;
    align-items: center;
}

.seller-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    margin-right: 8px;
    border: 2px solid #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.seller-name {
    font-size: 13px;
    color: #666;
}

.product-actions {
    display: flex;
    gap: 10px;
}

.like-count, .comment-count {
    font-size: 13px;
    color: #999;
    display: flex;
    align-items: center;
    gap: 4px;
    transition: all 0.3s ease;
}

.like-count:hover, .comment-count:hover {
    color: #ff6f06;
}

/* Banner增强 */
.banner {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    margin-bottom: 30px;
}

.banner img {
    transition: transform 0.5s ease;
}

.banner:hover img {
    transform: scale(1.03);
}

.banner-content {
    background: linear-gradient(90deg, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.4) 50%, rgba(0,0,0,0.1) 100%);
}

.banner-content h2 {
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 15px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.banner-content p {
    font-size: 18px;
    margin-bottom: 25px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.banner-btn {
    background: linear-gradient(135deg, #ff7e1a, #ff5500);
    padding: 12px 28px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 16px;
    box-shadow: 0 4px 10px rgba(255, 111, 6, 0.3);
    transition: all 0.3s ease;
}

.banner-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(255, 111, 6, 0.4);
}

/* 特色服务增强 */
.feature-services {
    background-color: #fff;
    border-radius: 10px;
    padding: 20px;
    margin: 30px 0;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
}

.feature-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    padding: 15px;
    transition: all 0.3s ease;
    border-radius: 8px;
}

.feature-item:hover {
    background-color: #fff8f3;
    transform: translateY(-5px);
}

.feature-item i {
    font-size: 28px;
    color: #ff6f06;
    background-color: #fff2e8;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin-bottom: 5px;
    transition: all 0.3s ease;
}

.feature-item:hover i {
    background-color: #ff6f06;
    color: #fff;
    box-shadow: 0 5px 15px rgba(255, 111, 6, 0.3);
}

.feature-item span {
    font-size: 15px;
    font-weight: 500;
    color: #333;
}

/* 发布页面增强 */
.publish-container {
    background-color: #fff;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
}

.page-title {
    font-size: 26px;
    font-weight: 700;
    color: #333;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.form-label {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.form-input,
.form-select,
.form-textarea {
    padding: 12px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    border-color: #ff6f06;
    box-shadow: 0 0 0 3px rgba(255, 111, 6, 0.1);
}

.input-tips {
    font-size: 13px;
    color: #888;
    margin-top: 8px;
}

/* 商品类型选择器增强 */
.product-type-selector {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
}

.type-option {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: #f9f9f9;
}

.type-option:hover {
    background-color: #fff2e8;
    border-color: #ff6f06;
}

.type-option input[type="radio"] {
    margin-right: 10px;
    accent-color: #ff6f06;
    transform: scale(1.2);
}

.type-option label {
    font-size: 15px;
    font-weight: 500;
}

/* 图片上传增强 */
.upload-item {
    width: 130px;
    height: 130px;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
}

.add-image {
    border: 2px dashed #ddd;
    background-color: #f9f9f9;
    transition: all 0.3s ease;
}

.add-image:hover {
    border-color: #ff6f06;
    background-color: #fff2e8;
}

.add-image i {
    font-size: 28px;
    color: #ff6f06;
    margin-bottom: 8px;
}

/* 底部信息增强 */
.footer {
    background-color: #fff;
    padding: 50px 0 30px;
    border-top: 1px solid #eee;
    margin-top: 50px;
}

.footer-links {
    display: flex;
    justify-content: space-between;
    margin-bottom: 40px;
}

.link-group h4 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
    position: relative;
    padding-bottom: 10px;
}

.link-group h4:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 30px;
    height: 2px;
    background-color: #ff6f06;
}

.link-group ul li {
    margin-bottom: 12px;
}

.link-group ul li a {
    color: #666;
    font-size: 14px;
    transition: all 0.3s ease;
    display: inline-block;
}

.link-group ul li a:hover {
    color: #ff6f06;
    transform: translateX(5px);
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.copyright, .footer-info {
    font-size: 13px;
    color: #999;
}

.footer-info a {
    color: #999;
    margin-left: 15px;
    transition: all 0.3s ease;
}

.footer-info a:hover {
    color: #ff6f06;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .banner-content h2 {
        font-size: 24px;
    }
    
    .banner-content p {
        font-size: 14px;
    }
    
    .feature-item {
        padding: 10px;
    }
    
    .feature-item i {
        width: 40px;
        height: 40px;
        font-size: 20px;
    }
    
    .feature-item span {
        font-size: 13px;
    }
    
    .product-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .publish-container {
        padding: 20px 15px;
    }
}