<?php
require_once 'config/database.php';

// 用户认证函数
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }
    
    global $pdo;
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    return $stmt->fetch();
}

function login($username, $password) {
    global $pdo;
    
    $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ? OR email = ? OR phone = ?");
    $stmt->execute([$username, $username, $username]);
    $user = $stmt->fetch();
    
    if ($user && password_verify($password, $user['password'])) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        return true;
    }
    
    return false;
}

function logout() {
    session_destroy();
    header('Location: index.php');
    exit;
}

function register($data) {
    global $pdo;
    
    // 检查用户名、邮箱、手机号是否已存在
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = ? OR email = ? OR phone = ?");
    $stmt->execute([$data['username'], $data['email'], $data['phone']]);
    
    if ($stmt->fetchColumn() > 0) {
        return false;
    }
    
    // 创建新用户
    $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("INSERT INTO users (username, email, phone, password, nickname) VALUES (?, ?, ?, ?, ?)");
    
    return $stmt->execute([
        $data['username'],
        $data['email'], 
        $data['phone'],
        $hashedPassword,
        $data['nickname']
    ]);
}

// 商品相关函数
function getProducts($limit = 20, $offset = 0, $category = null, $search = null) {
    global $pdo;
    
    $sql = "SELECT p.*, u.nickname as seller_name, u.avatar as seller_avatar, c.name as category_name 
            FROM products p 
            JOIN users u ON p.user_id = u.id 
            JOIN categories c ON p.category_id = c.id 
            WHERE p.status = 'active'";
    
    $params = [];
    
    if ($category) {
        $sql .= " AND c.slug = ?";
        $params[] = $category;
    }
    
    if ($search) {
        $sql .= " AND (p.title LIKE ? OR p.description LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    
    $sql .= " ORDER BY p.created_at DESC LIMIT ? OFFSET ?";
    $params[] = $limit;
    $params[] = $offset;
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    
    return $stmt->fetchAll();
}

function getProduct($id) {
    global $pdo;
    
    $stmt = $pdo->prepare("
        SELECT p.*, u.nickname as seller_name, u.avatar as seller_avatar, u.rating as seller_rating,
               c.name as category_name, va.*
        FROM products p 
        JOIN users u ON p.user_id = u.id 
        JOIN categories c ON p.category_id = c.id 
        LEFT JOIN virtual_attributes va ON p.id = va.product_id
        WHERE p.id = ?
    ");
    $stmt->execute([$id]);
    
    return $stmt->fetch();
}

function incrementViews($productId) {
    global $pdo;
    
    $stmt = $pdo->prepare("UPDATE products SET views = views + 1 WHERE id = ?");
    $stmt->execute([$productId]);
}

function getCategories() {
    global $pdo;
    
    $stmt = $pdo->query("SELECT * FROM categories ORDER BY sort_order");
    return $stmt->fetchAll();
}

// 文件上传函数
function uploadFile($file, $allowedTypes = ['jpg', 'jpeg', 'png', 'gif']) {
    if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
        return false;
    }
    
    $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    
    if (!in_array($fileExtension, $allowedTypes)) {
        return false;
    }
    
    if ($file['size'] > MAX_FILE_SIZE) {
        return false;
    }
    
    $fileName = uniqid() . '.' . $fileExtension;
    $uploadPath = UPLOAD_PATH . $fileName;
    
    if (!is_dir(UPLOAD_PATH)) {
        mkdir(UPLOAD_PATH, 0755, true);
    }
    
    if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
        return $fileName;
    }
    
    return false;
}

// 工具函数
function formatPrice($price) {
    return '¥' . number_format($price, 2);
}

function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return '刚刚';
    if ($time < 3600) return floor($time/60) . '分钟前';
    if ($time < 86400) return floor($time/3600) . '小时前';
    if ($time < 2592000) return floor($time/86400) . '天前';
    if ($time < 31536000) return floor($time/2592000) . '个月前';
    
    return floor($time/31536000) . '年前';
}

function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

function redirect($url) {
    header("Location: $url");
    exit;
}
?>
