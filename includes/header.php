<?php
require_once 'includes/functions.php';
$currentUser = getCurrentUser();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' . SITE_NAME : SITE_NAME . ' - 虚拟商品交易平台'; ?></title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/bootstrap-icons.css">
    <link rel="stylesheet" href="css/virtual-product.css">
    <link rel="stylesheet" href="css/theme-colors.css">
    <link rel="stylesheet" href="css/enhanced-style.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="css/fonts.css">
    <?php if (isset($additionalCSS)): ?>
        <?php foreach ($additionalCSS as $css): ?>
            <link rel="stylesheet" href="<?php echo $css; ?>">
        <?php endforeach; ?>
    <?php endif; ?>
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <a href="index.php">
                    <img src="images/logo.svg" alt="数字鱼logo">
                    <span class="logo-text">数字鱼</span>
                </a>
            </div>
            <div class="search-box">
                <form action="search.php" method="GET">
                    <input type="text" name="q" placeholder="搜索你想要的虚拟商品" value="<?php echo isset($_GET['q']) ? htmlspecialchars($_GET['q']) : ''; ?>">
                    <button type="submit" class="search-btn"><i class="bi bi-search"></i></button>
                </form>
            </div>
            <nav class="nav">
                <ul>
                    <li><a href="index.php" <?php echo basename($_SERVER['PHP_SELF']) == 'index.php' ? 'class="active"' : ''; ?>>首页</a></li>
                    <li><a href="list.php">同城</a></li>
                    <li><a href="publish.php" class="publish-btn" <?php echo basename($_SERVER['PHP_SELF']) == 'publish.php' ? 'active' : ''; ?>>发布商品</a></li>
                    <li><a href="message.php">消息</a></li>
                    <li><a href="member.php" <?php echo basename($_SERVER['PHP_SELF']) == 'member.php' ? 'class="active"' : ''; ?>>我的</a></li>
                </ul>
            </nav>
            <div class="user-info">
                <?php if ($currentUser): ?>
                    <div class="user-dropdown">
                        <a href="member.php" class="user-avatar">
                            <img src="<?php echo $currentUser['avatar']; ?>" alt="用户头像">
                            <span><?php echo htmlspecialchars($currentUser['nickname']); ?></span>
                        </a>
                        <div class="dropdown-menu">
                            <a href="member.php">个人中心</a>
                            <a href="my-products.php">我的发布</a>
                            <a href="my-orders.php">我的订单</a>
                            <a href="logout.php">退出登录</a>
                        </div>
                    </div>
                <?php else: ?>
                    <a href="login.php" class="login-btn">登录/注册</a>
                <?php endif; ?>
            </div>
        </div>
    </header>

    <?php if (isset($_SESSION['success_message'])): ?>
        <div class="alert alert-success">
            <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="alert alert-error">
            <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
        </div>
    <?php endif; ?>
