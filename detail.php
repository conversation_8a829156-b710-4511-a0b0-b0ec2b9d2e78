<?php
require_once 'includes/header.php';

$productId = intval($_GET['id'] ?? 0);

if (!$productId) {
    redirect('index.php');
}

// 获取商品详情
$product = getProduct($productId);

if (!$product) {
    $_SESSION['error_message'] = '商品不存在';
    redirect('index.php');
}

// 增加浏览次数
incrementViews($productId);

// 记录浏览历史（如果用户已登录）
if (isLoggedIn()) {
    try {
        $stmt = $pdo->prepare("INSERT INTO browse_history (user_id, product_id) VALUES (?, ?) ON DUPLICATE KEY UPDATE created_at = NOW()");
        $stmt->execute([$_SESSION['user_id'], $productId]);
    } catch (Exception $e) {
        // 忽略错误
    }
}

$pageTitle = htmlspecialchars($product['title']);
$images = json_decode($product['images'], true) ?: ['images/product-default.svg'];

// 获取相似商品
$similarProducts = getProducts(4, 0, null, null);
?>

<!-- 主要内容区 -->
<main class="main detail-page">
    <div class="container">
        <!-- 商品详情 -->
        <div class="product-detail">
            <!-- 面包屑导航 -->
            <div class="breadcrumb">
                <a href="index.php">首页</a> &gt; 
                <a href="list.php?category=<?php echo $product['category_name']; ?>"><?php echo htmlspecialchars($product['category_name']); ?></a> &gt; 
                <span class="current-category"><?php echo htmlspecialchars($product['title']); ?></span>
            </div>

            <!-- 商品画廊 -->
            <div class="product-gallery">
                <div class="gallery-thumbs">
                    <?php foreach ($images as $index => $image): ?>
                        <div class="gallery-thumb <?php echo $index === 0 ? 'active' : ''; ?>">
                            <img src="<?php echo $image; ?>" alt="商品图片<?php echo $index + 1; ?>">
                        </div>
                    <?php endforeach; ?>
                </div>
                <div class="gallery-main">
                    <div class="gallery-main-img">
                        <img src="<?php echo $images[0]; ?>" alt="<?php echo htmlspecialchars($product['title']); ?>">
                    </div>
                </div>
            </div>

            <!-- 商品信息 -->
            <div class="product-header">
                <h1><?php echo htmlspecialchars($product['title']); ?></h1>
                <div class="product-price-info">
                    <span class="detail-price"><?php echo formatPrice($product['price']); ?></span>
                    <?php if ($product['original_price']): ?>
                        <span class="detail-original-price"><?php echo formatPrice($product['original_price']); ?></span>
                    <?php endif; ?>
                </div>
                <div class="product-meta-info">
                    <?php if ($product['is_virtual']): ?>
                        <div class="meta-item">
                            <i class="bi bi-lightning-charge"></i>
                            <span>即时交付</span>
                        </div>
                    <?php endif; ?>
                    <div class="meta-item">
                        <i class="bi bi-shield-check"></i>
                        <span>担保交易</span>
                    </div>
                    <div class="meta-item">
                        <i class="bi bi-eye"></i>
                        <span>浏览 <?php echo $product['views']; ?> 次</span>
                    </div>
                    <div class="meta-item">
                        <i class="bi bi-calendar3"></i>
                        <span>发布于 <?php echo date('Y-m-d', strtotime($product['created_at'])); ?></span>
                    </div>
                </div>
            </div>

            <!-- 商品描述 -->
            <div class="product-description">
                <h2 class="description-title">商品描述</h2>
                <div class="description-content">
                    <?php echo nl2br(htmlspecialchars($product['description'])); ?>
                </div>
            </div>

            <!-- 虚拟商品属性 -->
            <?php if ($product['is_virtual'] && $product['usage_period']): ?>
                <div class="product-attributes">
                    <h2 class="description-title">商品属性</h2>
                    <div class="attributes-list">
                        <div class="attribute-item">
                            <div class="attribute-label">商品类型</div>
                            <div class="attribute-value">虚拟商品</div>
                        </div>
                        <div class="attribute-item">
                            <div class="attribute-label">使用期限</div>
                            <div class="attribute-value">
                                <?php 
                                if ($product['usage_period'] === 'unlimited') {
                                    echo '永久有效';
                                } else {
                                    echo $product['period_value'] . ' ' . 
                                         ($product['period_unit'] === 'day' ? '天' : 
                                          ($product['period_unit'] === 'month' ? '个月' : '年'));
                                }
                                ?>
                            </div>
                        </div>
                        <div class="attribute-item">
                            <div class="attribute-label">发货方式</div>
                            <div class="attribute-value">
                                <?php echo $product['delivery_method'] === 'automatic' ? '自动发货' : '手动发货'; ?>
                            </div>
                        </div>
                        <?php if ($product['platform']): ?>
                            <div class="attribute-item">
                                <div class="attribute-label">使用平台</div>
                                <div class="attribute-value"><?php echo htmlspecialchars($product['platform']); ?></div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- 交易按钮 -->
            <div class="trade-actions">
                <?php if (isLoggedIn() && $_SESSION['user_id'] != $product['user_id']): ?>
                    <button class="btn btn-primary" onclick="buyNow(<?php echo $product['id']; ?>)">立即购买</button>
                    <button class="btn btn-outline" onclick="addToCart(<?php echo $product['id']; ?>)">加入购物车</button>
                    <button class="btn btn-outline" onclick="toggleFavorite(<?php echo $product['id']; ?>)">
                        <i class="bi bi-heart"></i> 收藏
                    </button>
                <?php elseif (!isLoggedIn()): ?>
                    <a href="login.php" class="btn btn-primary">登录后购买</a>
                <?php else: ?>
                    <button class="btn btn-outline" disabled>这是您发布的商品</button>
                <?php endif; ?>
                <button class="btn btn-outline" onclick="shareProduct()">
                    <i class="bi bi-share"></i> 分享
                </button>
            </div>
        </div>

        <!-- 侧边栏 -->
        <div class="sidebar">
            <!-- 卖家信息 -->
            <div class="seller-card">
                <div class="seller-profile">
                    <img src="<?php echo $product['seller_avatar']; ?>" alt="卖家头像" class="seller-profile-avatar">
                    <div class="seller-profile-info">
                        <h3><?php echo htmlspecialchars($product['seller_name']); ?></h3>
                        <div class="seller-badges">
                            <span class="seller-badge"><i class="bi bi-star"></i> 评分 <?php echo $product['seller_rating']; ?></span>
                        </div>
                    </div>
                </div>
                <div class="seller-stats">
                    <div class="stat-item">
                        <span class="stat-value"><?php echo number_format($product['seller_rating'], 1); ?></span>
                        <span class="stat-label">评分</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value"><?php echo $product['total_sales'] ?? 0; ?></span>
                        <span class="stat-label">已卖出</span>
                    </div>
                </div>
                <?php if (isLoggedIn() && $_SESSION['user_id'] != $product['user_id']): ?>
                    <a href="#" class="contact-seller-btn">联系卖家</a>
                <?php endif; ?>
            </div>

            <!-- 相似商品推荐 -->
            <div class="similar-products">
                <h3>相似商品推荐</h3>
                <?php foreach ($similarProducts as $similar): ?>
                    <div class="similar-product-item">
                        <a href="detail.php?id=<?php echo $similar['id']; ?>">
                            <div class="similar-product-img">
                                <?php 
                                $similarImages = json_decode($similar['images'], true);
                                $similarImage = $similarImages ? $similarImages[0] : 'images/product-default.svg';
                                ?>
                                <img src="<?php echo $similarImage; ?>" alt="<?php echo htmlspecialchars($similar['title']); ?>">
                            </div>
                            <div class="similar-product-info">
                                <h4><?php echo htmlspecialchars(mb_substr($similar['title'], 0, 30)); ?></h4>
                                <div class="similar-product-price"><?php echo formatPrice($similar['price']); ?></div>
                            </div>
                        </a>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</main>

<script>
function buyNow(productId) {
    // 实现立即购买功能
    alert('购买功能开发中...');
}

function addToCart(productId) {
    // 实现加入购物车功能
    alert('购物车功能开发中...');
}

function toggleFavorite(productId) {
    // 实现收藏功能
    fetch('api/favorite.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({product_id: productId})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
        } else {
            alert('操作失败');
        }
    });
}

function shareProduct() {
    // 实现分享功能
    if (navigator.share) {
        navigator.share({
            title: '<?php echo htmlspecialchars($product['title']); ?>',
            url: window.location.href
        });
    } else {
        // 复制链接到剪贴板
        navigator.clipboard.writeText(window.location.href);
        alert('链接已复制到剪贴板');
    }
}

// 图片画廊功能
document.querySelectorAll('.gallery-thumb').forEach(thumb => {
    thumb.addEventListener('click', function() {
        document.querySelectorAll('.gallery-thumb').forEach(t => t.classList.remove('active'));
        this.classList.add('active');
        
        const img = this.querySelector('img');
        document.querySelector('.gallery-main-img img').src = img.src;
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
