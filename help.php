<?php
$pageTitle = '帮助中心';
$additionalCSS = ['css/help.css'];
require_once 'includes/header.php';
?>

<!-- 主要内容区 -->
<main class="main help-page">
    <div class="container">
        <div class="help-container">
            <!-- 页面标题 -->
            <div class="page-header">
                <h1 class="page-title">帮助中心</h1>
                <p class="page-subtitle">为您提供全面的使用指南和常见问题解答</p>
            </div>

            <!-- 搜索框 -->
            <div class="help-search">
                <div class="search-box">
                    <i class="bi bi-search"></i>
                    <input type="text" placeholder="搜索您遇到的问题..." id="helpSearch">
                </div>
            </div>

            <!-- 快速导航 -->
            <div class="quick-nav">
                <div class="nav-item" data-category="getting-started">
                    <div class="nav-icon">
                        <i class="bi bi-play-circle"></i>
                    </div>
                    <h3>新手入门</h3>
                    <p>了解平台基本功能</p>
                </div>
                <div class="nav-item" data-category="selling">
                    <div class="nav-icon">
                        <i class="bi bi-box-seam"></i>
                    </div>
                    <h3>发布商品</h3>
                    <p>学习如何发布虚拟商品</p>
                </div>
                <div class="nav-item" data-category="buying">
                    <div class="nav-icon">
                        <i class="bi bi-bag"></i>
                    </div>
                    <h3>购买指南</h3>
                    <p>安全购买虚拟商品</p>
                </div>
                <div class="nav-item" data-category="account">
                    <div class="nav-icon">
                        <i class="bi bi-person-gear"></i>
                    </div>
                    <h3>账户管理</h3>
                    <p>管理您的账户信息</p>
                </div>
            </div>

            <!-- 帮助内容 -->
            <div class="help-content">
                <!-- 新手入门 -->
                <div class="help-section" id="getting-started">
                    <h2>新手入门</h2>
                    <div class="faq-list">
                        <div class="faq-item">
                            <div class="faq-question">
                                <h3>什么是数字鱼平台？</h3>
                                <i class="bi bi-chevron-down"></i>
                            </div>
                            <div class="faq-answer">
                                <p>数字鱼是一个专注于虚拟商品交易的平台，用户可以在这里安全地买卖各种虚拟商品，如游戏道具、软件激活码、数字内容等。</p>
                            </div>
                        </div>
                        <div class="faq-item">
                            <div class="faq-question">
                                <h3>如何注册账户？</h3>
                                <i class="bi bi-chevron-down"></i>
                            </div>
                            <div class="faq-answer">
                                <p>点击页面右上角的"登录/注册"按钮，填写手机号码或邮箱地址，设置密码即可完成注册。建议完善个人信息以提高账户安全性。</p>
                            </div>
                        </div>
                        <div class="faq-item">
                            <div class="faq-question">
                                <h3>平台有哪些安全保障？</h3>
                                <i class="bi bi-chevron-down"></i>
                            </div>
                            <div class="faq-answer">
                                <p>我们提供多重安全保障：实名认证、交易担保、7天无理由退款、24小时客服支持等，确保您的交易安全。</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 发布商品 -->
                <div class="help-section" id="selling">
                    <h2>发布商品</h2>
                    <div class="faq-list">
                        <div class="faq-item">
                            <div class="faq-question">
                                <h3>如何发布虚拟商品？</h3>
                                <i class="bi bi-chevron-down"></i>
                            </div>
                            <div class="faq-answer">
                                <p>登录后点击"发布商品"按钮，选择商品分类，填写商品信息、上传图片、设置价格，最后点击"立即发布"即可。</p>
                            </div>
                        </div>
                        <div class="faq-item">
                            <div class="faq-question">
                                <h3>商品审核需要多长时间？</h3>
                                <i class="bi bi-chevron-down"></i>
                            </div>
                            <div class="faq-answer">
                                <p>一般情况下，商品审核会在24小时内完成。审核通过后，您的商品将正式上架销售。</p>
                            </div>
                        </div>
                        <div class="faq-item">
                            <div class="faq-question">
                                <h3>如何提高商品销量？</h3>
                                <i class="bi bi-chevron-down"></i>
                            </div>
                            <div class="faq-answer">
                                <p>建议：1）上传清晰的商品图片；2）详细描述商品功能；3）合理定价；4）及时回复买家咨询；5）提供优质的售后服务。</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 购买指南 -->
                <div class="help-section" id="buying">
                    <h2>购买指南</h2>
                    <div class="faq-list">
                        <div class="faq-item">
                            <div class="faq-question">
                                <h3>如何购买虚拟商品？</h3>
                                <i class="bi bi-chevron-down"></i>
                            </div>
                            <div class="faq-answer">
                                <p>浏览商品页面，点击"立即购买"，选择数量，确认订单信息，选择支付方式完成付款即可。</p>
                            </div>
                        </div>
                        <div class="faq-item">
                            <div class="faq-question">
                                <h3>支持哪些支付方式？</h3>
                                <i class="bi bi-chevron-down"></i>
                            </div>
                            <div class="faq-answer">
                                <p>我们支持支付宝、微信支付、银行卡支付等多种支付方式，确保您的支付便捷安全。</p>
                            </div>
                        </div>
                        <div class="faq-item">
                            <div class="faq-question">
                                <h3>如何申请退款？</h3>
                                <i class="bi bi-chevron-down"></i>
                            </div>
                            <div class="faq-answer">
                                <p>在订单详情页面点击"申请退款"，选择退款原因，提交申请。我们会在3个工作日内处理您的退款申请。</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 账户管理 -->
                <div class="help-section" id="account">
                    <h2>账户管理</h2>
                    <div class="faq-list">
                        <div class="faq-item">
                            <div class="faq-question">
                                <h3>如何修改个人信息？</h3>
                                <i class="bi bi-chevron-down"></i>
                            </div>
                            <div class="faq-answer">
                                <p>进入个人中心，点击"账号设置"，可以修改头像、昵称、联系方式等个人信息。</p>
                            </div>
                        </div>
                        <div class="faq-item">
                            <div class="faq-question">
                                <h3>忘记密码怎么办？</h3>
                                <i class="bi bi-chevron-down"></i>
                            </div>
                            <div class="faq-answer">
                                <p>在登录页面点击"忘记密码"，输入注册时的手机号或邮箱，按照提示重置密码即可。</p>
                            </div>
                        </div>
                        <div class="faq-item">
                            <div class="faq-question">
                                <h3>如何提高账户安全性？</h3>
                                <i class="bi bi-chevron-down"></i>
                            </div>
                            <div class="faq-answer">
                                <p>建议：1）设置复杂密码；2）绑定手机号和邮箱；3）开启登录验证；4）定期更换密码；5）不要在公共设备上保存登录信息。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 联系客服 -->
            <div class="contact-section">
                <div class="contact-card">
                    <div class="contact-icon">
                        <i class="bi bi-headset"></i>
                    </div>
                    <div class="contact-content">
                        <h3>还有其他问题？</h3>
                        <p>我们的客服团队随时为您提供帮助</p>
                        <div class="contact-methods">
                            <a href="#" class="contact-btn">
                                <i class="bi bi-chat-dots"></i>
                                在线客服
                            </a>
                            <a href="mailto:<EMAIL>" class="contact-btn">
                                <i class="bi bi-envelope"></i>
                                邮件咨询
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // FAQ 折叠功能
    const faqItems = document.querySelectorAll('.faq-item');
    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');
        const answer = item.querySelector('.faq-answer');
        const icon = question.querySelector('i');
        
        question.addEventListener('click', function() {
            const isOpen = item.classList.contains('open');
            
            // 关闭所有其他FAQ
            faqItems.forEach(otherItem => {
                otherItem.classList.remove('open');
                otherItem.querySelector('.faq-answer').style.maxHeight = '0';
                otherItem.querySelector('.faq-question i').style.transform = 'rotate(0deg)';
            });
            
            // 切换当前FAQ
            if (!isOpen) {
                item.classList.add('open');
                answer.style.maxHeight = answer.scrollHeight + 'px';
                icon.style.transform = 'rotate(180deg)';
            }
        });
    });
    
    // 快速导航
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        item.addEventListener('click', function() {
            const category = this.dataset.category;
            const section = document.getElementById(category);
            if (section) {
                section.scrollIntoView({ behavior: 'smooth' });
            }
        });
    });
    
    // 搜索功能
    const searchInput = document.getElementById('helpSearch');
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const faqItems = document.querySelectorAll('.faq-item');
        
        faqItems.forEach(item => {
            const question = item.querySelector('.faq-question h3').textContent.toLowerCase();
            const answer = item.querySelector('.faq-answer p').textContent.toLowerCase();
            
            if (question.includes(searchTerm) || answer.includes(searchTerm)) {
                item.style.display = 'block';
            } else {
                item.style.display = searchTerm ? 'none' : 'block';
            }
        });
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
