<?php
$pageTitle = '发布商品';
$additionalCSS = ['css/publish.css', 'css/publish-enhanced.css'];
$additionalJS = ['js/publish.js'];
require_once 'includes/header.php';

// 检查登录状态
if (!isLoggedIn()) {
    $_SESSION['error_message'] = '请先登录后再发布商品';
    redirect('login.php');
}

$categories = getCategories();
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $title = sanitizeInput($_POST['productTitle']);
    $description = sanitizeInput($_POST['productDescription']);
    $price = floatval($_POST['productPrice']);
    $originalPrice = !empty($_POST['originalPrice']) ? floatval($_POST['originalPrice']) : null;
    $categoryId = intval($_POST['virtualCategory']);
    $stock = intval($_POST['inventory']);
    $isVirtual = $_POST['productType'] === 'virtual';
    
    // 验证必填字段
    if (empty($title) || empty($description) || $price <= 0 || $categoryId <= 0) {
        $error = '请填写完整的商品信息';
    } else {
        try {
            // 处理图片上传
            $images = [];
            if (isset($_FILES['images'])) {
                foreach ($_FILES['images']['tmp_name'] as $key => $tmpName) {
                    if (!empty($tmpName)) {
                        $file = [
                            'name' => $_FILES['images']['name'][$key],
                            'tmp_name' => $tmpName,
                            'size' => $_FILES['images']['size'][$key],
                            'error' => $_FILES['images']['error'][$key]
                        ];
                        
                        $uploadedFile = uploadFile($file);
                        if ($uploadedFile) {
                            $images[] = UPLOAD_PATH . $uploadedFile;
                        }
                    }
                }
            }
            
            // 插入商品数据
            $stmt = $pdo->prepare("
                INSERT INTO products (user_id, category_id, title, description, price, original_price, stock, is_virtual, images) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $_SESSION['user_id'],
                $categoryId,
                $title,
                $description,
                $price,
                $originalPrice,
                $stock,
                $isVirtual,
                json_encode($images)
            ]);
            
            $productId = $pdo->lastInsertId();
            
            // 如果是虚拟商品，插入虚拟商品属性
            if ($isVirtual) {
                $usagePeriod = $_POST['usagePeriod'] ?? 'unlimited';
                $periodValue = !empty($_POST['periodValue']) ? intval($_POST['periodValue']) : null;
                $periodUnit = $_POST['periodUnit'] ?? null;
                $deliveryMethod = $_POST['deliveryMethod'] ?? 'manual';
                $platform = sanitizeInput($_POST['platform'] ?? '');
                $textContent = sanitizeInput($_POST['textContent'] ?? '');
                $instructions = sanitizeInput($_POST['instructions'] ?? '');
                
                // 处理服务选项
                $services = [];
                if (isset($_POST['serviceRefund'])) $services[] = 'refund';
                if (isset($_POST['serviceReplace'])) $services[] = 'replace';
                if (isset($_POST['serviceConsult'])) $services[] = 'consult';
                if (isset($_POST['serviceInvoice'])) $services[] = 'invoice';
                
                $stmt = $pdo->prepare("
                    INSERT INTO virtual_attributes 
                    (product_id, usage_period, period_value, period_unit, delivery_method, platform, content_text, instructions, services) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");
                
                $stmt->execute([
                    $productId,
                    $usagePeriod,
                    $periodValue,
                    $periodUnit,
                    $deliveryMethod,
                    $platform,
                    $textContent,
                    $instructions,
                    json_encode($services)
                ]);
            }
            
            $_SESSION['success_message'] = '商品发布成功！';
            redirect('detail.php?id=' . $productId);
            
        } catch (Exception $e) {
            $error = '发布失败：' . $e->getMessage();
        }
    }
}
?>

<!-- 主要内容区 -->
<main class="main publish-page">
    <div class="container">
        <div class="publish-container">
            <h1 class="page-title">发布商品</h1>
            
            <?php if ($error): ?>
                <div class="alert alert-error"><?php echo $error; ?></div>
            <?php endif; ?>
            
            <form class="publish-form" id="publishForm" method="POST" enctype="multipart/form-data">
                <!-- 商品类型选择 -->
                <div class="form-group">
                    <label class="form-label">商品类型</label>
                    <div class="product-type-selector">
                        <div class="type-option">
                            <input type="radio" name="productType" id="typePhysical" value="physical">
                            <label for="typePhysical">实物商品</label>
                        </div>
                        <div class="type-option">
                            <input type="radio" name="productType" id="typeVirtual" value="virtual" checked>
                            <label for="typeVirtual">虚拟商品</label>
                        </div>
                    </div>
                </div>
                
                <!-- 虚拟商品分类 -->
                <div class="form-group" id="virtualCategoryGroup">
                    <label class="form-label">虚拟商品分类</label>
                    <div class="virtual-category-selector">
                        <select class="form-select" id="virtualCategory" name="virtualCategory" required>
                            <option value="">请选择分类</option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?php echo $category['id']; ?>"><?php echo htmlspecialchars($category['name']); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                
                <!-- 商品标题 -->
                <div class="form-group">
                    <label class="form-label">商品标题</label>
                    <input type="text" class="form-input" id="productTitle" name="productTitle" placeholder="请输入商品标题，最多30字" maxlength="30" required>
                    <div class="input-tips">标题越详细，越容易被买家搜索到</div>
                </div>
                
                <!-- 商品图片 -->
                <div class="form-group">
                    <label class="form-label">商品图片</label>
                    <div class="image-uploader">
                        <div class="upload-preview">
                            <div class="upload-item add-image">
                                <i class="bi bi-plus-lg"></i>
                                <span>添加图片</span>
                                <input type="file" class="file-input" id="imageUpload" name="images[]" multiple accept="image/*">
                            </div>
                        </div>
                        <div class="upload-tips">
                            <p>最多上传9张图片，单张不超过5MB</p>
                            <p>对于虚拟商品，请上传商品截图、使用效果图等</p>
                        </div>
                    </div>
                </div>
                
                <!-- 商品描述 -->
                <div class="form-group">
                    <label class="form-label">商品描述</label>
                    <textarea class="form-textarea" id="productDescription" name="productDescription" placeholder="请详细描述商品的内容、用途、使用方法等信息，让买家更好地了解商品" rows="6" required></textarea>
                </div>

                <!-- 虚拟商品属性 -->
                <div class="form-group" id="virtualAttributesGroup" style="display: none;">
                    <label class="form-label">虚拟商品属性</label>
                    <div class="virtual-attributes">
                        <!-- 使用期限 -->
                        <div class="attribute-item">
                            <label class="attribute-label">使用期限</label>
                            <select class="form-select" id="usagePeriod" name="usagePeriod">
                                <option value="unlimited">永久有效</option>
                                <option value="limited">限时有效</option>
                            </select>
                            <div class="period-input" style="display: none;">
                                <input type="number" class="form-input" id="periodValue" name="periodValue" min="1" placeholder="数量">
                                <select class="form-select" id="periodUnit" name="periodUnit">
                                    <option value="day">天</option>
                                    <option value="month">月</option>
                                    <option value="year">年</option>
                                </select>
                            </div>
                        </div>

                        <!-- 发货方式 -->
                        <div class="attribute-item">
                            <label class="attribute-label">发货方式</label>
                            <select class="form-select" id="deliveryMethod" name="deliveryMethod">
                                <option value="automatic">自动发货</option>
                                <option value="manual">手动发货</option>
                            </select>
                        </div>

                        <!-- 使用平台 -->
                        <div class="attribute-item">
                            <label class="attribute-label">使用平台</label>
                            <input type="text" class="form-input" id="platform" name="platform" placeholder="如：iOS/安卓/Windows/Mac等">
                        </div>
                    </div>
                </div>

                <!-- 商品内容 -->
                <div class="form-group" id="contentGroup" style="display: none;">
                    <label class="form-label">商品内容</label>
                    <div class="content-tabs">
                        <div class="content-tab active" data-tab="contentText">文本内容</div>
                        <div class="content-tab" data-tab="contentFile">附件内容</div>
                    </div>

                    <div class="content-panels">
                        <!-- 文本内容面板 -->
                        <div class="content-panel active" id="contentText">
                            <textarea class="form-textarea" id="textContent" name="textContent" placeholder="请输入激活码、账号密码等文本内容，买家付款后可见" rows="4"></textarea>
                            <div class="input-tips">该内容仅在买家付款后可见，请勿在商品描述中透露</div>
                        </div>

                        <!-- 附件内容面板 -->
                        <div class="content-panel" id="contentFile">
                            <div class="file-uploader">
                                <div class="upload-btn">
                                    <i class="bi bi-file-earmark-arrow-up"></i>
                                    <span>上传附件</span>
                                    <input type="file" class="file-input" id="fileUpload" name="contentFile">
                                </div>
                                <div class="upload-tips">
                                    <p>支持PDF、ZIP、RAR等格式，单个文件不超过50MB</p>
                                    <p>附件内容仅在买家付款后可下载</p>
                                </div>
                            </div>
                            <div class="file-list">
                                <!-- 上传的文件将在这里显示 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 使用说明 -->
                <div class="form-group" id="instructionsGroup" style="display: none;">
                    <label class="form-label">使用说明</label>
                    <textarea class="form-textarea" id="instructions" name="instructions" placeholder="请输入商品的使用方法、注意事项等信息" rows="4"></textarea>
                </div>

                <!-- 售后服务 -->
                <div class="form-group" id="serviceGroup" style="display: none;">
                    <label class="form-label">售后服务</label>
                    <div class="service-options">
                        <label class="checkbox-label">
                            <input type="checkbox" id="serviceRefund" name="serviceRefund"> 支持退款
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="serviceReplace" name="serviceReplace"> 支持更换
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="serviceConsult" name="serviceConsult"> 提供技术支持
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="serviceInvoice" name="serviceInvoice"> 提供发票
                        </label>
                    </div>
                    <textarea class="form-textarea" id="serviceDescription" name="serviceDescription" placeholder="请详细描述您提供的售后服务内容和政策" rows="3"></textarea>
                </div>

                <!-- 商品价格 -->
                <div class="form-group">
                    <label class="form-label">商品价格</label>
                    <div class="price-input">
                        <span class="price-symbol">¥</span>
                        <input type="number" class="form-input" id="productPrice" name="productPrice" placeholder="0.00" min="0" step="0.01" required>
                    </div>
                </div>
                
                <!-- 原价展示 -->
                <div class="form-group">
                    <label class="form-label">原价展示（选填）</label>
                    <div class="price-input">
                        <span class="price-symbol">¥</span>
                        <input type="number" class="form-input" id="originalPrice" name="originalPrice" placeholder="0.00" min="0" step="0.01">
                    </div>
                </div>
                
                <!-- 库存数量 -->
                <div class="form-group">
                    <label class="form-label">库存数量</label>
                    <input type="number" class="form-input" id="inventory" name="inventory" placeholder="请输入库存数量" min="1" value="1" required>
                </div>
                
                <!-- 提交按钮 -->
                <div class="form-actions">
                    <button type="button" class="btn btn-outline" id="saveDraft">保存草稿</button>
                    <button type="submit" class="btn btn-primary" id="publishBtn">立即发布</button>
                </div>
            </form>
        </div>
    </div>
</main>

<script>
// 商品类型切换
document.addEventListener('DOMContentLoaded', function() {
    const typeRadios = document.querySelectorAll('input[name="productType"]');
    const virtualCategoryGroup = document.getElementById('virtualCategoryGroup');
    const virtualAttributesGroup = document.getElementById('virtualAttributesGroup');
    const contentGroup = document.getElementById('contentGroup');
    const instructionsGroup = document.getElementById('instructionsGroup');
    const serviceGroup = document.getElementById('serviceGroup');

    function toggleVirtualFields(isVirtual) {
        const display = isVirtual ? 'block' : 'none';
        if (virtualCategoryGroup) virtualCategoryGroup.style.display = display;
        if (virtualAttributesGroup) virtualAttributesGroup.style.display = display;
        if (contentGroup) contentGroup.style.display = display;
        if (instructionsGroup) instructionsGroup.style.display = display;
        if (serviceGroup) serviceGroup.style.display = display;

        // 更新必填字段
        const virtualCategory = document.getElementById('virtualCategory');
        if (virtualCategory) {
            virtualCategory.required = isVirtual;
        }
    }

    typeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            toggleVirtualFields(this.value === 'virtual');
        });
    });

    // 初始化显示状态
    const checkedRadio = document.querySelector('input[name="productType"]:checked');
    if (checkedRadio) {
        toggleVirtualFields(checkedRadio.value === 'virtual');
    }

    // 使用期限切换
    const usagePeriodSelect = document.getElementById('usagePeriod');
    const periodInput = document.querySelector('.period-input');

    if (usagePeriodSelect && periodInput) {
        usagePeriodSelect.addEventListener('change', function() {
            periodInput.style.display = this.value === 'limited' ? 'flex' : 'none';
        });
    }

    // 内容标签切换
    const contentTabs = document.querySelectorAll('.content-tab');
    const contentPanels = document.querySelectorAll('.content-panel');

    contentTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const tabType = this.dataset.tab;

            // 更新标签状态
            contentTabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');

            // 更新面板显示
            contentPanels.forEach(panel => {
                panel.classList.remove('active');
                if (panel.id === tabType) {
                    panel.classList.add('active');
                }
            });
        });
    });

    // 图片上传预览
    const imageUpload = document.getElementById('imageUpload');
    const uploadPreview = document.querySelector('.upload-preview');

    if (imageUpload && uploadPreview) {
        imageUpload.addEventListener('change', function() {
            const files = this.files;

            // 清除现有预览（除了添加按钮）
            const existingPreviews = uploadPreview.querySelectorAll('.upload-item:not(.add-image)');
            existingPreviews.forEach(preview => preview.remove());

            // 添加新预览
            Array.from(files).forEach((file, index) => {
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const previewItem = document.createElement('div');
                        previewItem.className = 'upload-item';
                        previewItem.innerHTML = `
                            <img src="${e.target.result}" alt="预览图片">
                            <button type="button" class="remove-image" onclick="removeImage(this)">
                                <i class="bi bi-x"></i>
                            </button>
                        `;
                        uploadPreview.insertBefore(previewItem, uploadPreview.querySelector('.add-image'));
                    };
                    reader.readAsDataURL(file);
                }
            });
        });
    }
});

// 移除图片预览
function removeImage(button) {
    button.parentElement.remove();
}
</script>

<?php require_once 'includes/footer.php'; ?>
