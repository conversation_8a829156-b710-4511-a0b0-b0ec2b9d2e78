<?php
$pageTitle = '消息中心';
$additionalCSS = ['css/member.css', 'css/messages.css'];
require_once 'includes/header.php';

// 检查登录状态
if (!isLoggedIn()) {
    $_SESSION['error_message'] = '请先登录';
    redirect('login.php');
}

$currentUser = getCurrentUser();

// 模拟消息数据（实际项目中应该从数据库获取）
$messages = [
    [
        'id' => 1,
        'type' => 'order',
        'title' => '订单支付成功',
        'content' => '您的订单 #202312150001 已支付成功，卖家将尽快为您发货。',
        'created_at' => '2023-12-15 14:30:00',
        'is_read' => false,
        'icon' => 'bi-bag-check'
    ],
    [
        'id' => 2,
        'type' => 'system',
        'title' => '账户安全提醒',
        'content' => '检测到您的账户在新设备上登录，如非本人操作请及时修改密码。',
        'created_at' => '2023-12-15 10:15:00',
        'is_read' => false,
        'icon' => 'bi-shield-exclamation'
    ],
    [
        'id' => 3,
        'type' => 'product',
        'title' => '商品审核通过',
        'content' => '您发布的商品"游戏激活码"已通过审核，现在可以正常销售了。',
        'created_at' => '2023-12-14 16:45:00',
        'is_read' => true,
        'icon' => 'bi-check-circle'
    ],
    [
        'id' => 4,
        'type' => 'promotion',
        'title' => '新用户福利',
        'content' => '恭喜您成为数字鱼的新用户！首次发布商品可享受免费推广服务。',
        'created_at' => '2023-12-14 09:20:00',
        'is_read' => true,
        'icon' => 'bi-gift'
    ]
];

// 统计未读消息
$unreadCount = count(array_filter($messages, function($msg) {
    return !$msg['is_read'];
}));
?>

<!-- 主要内容区 -->
<main class="main member-page">
    <div class="container">
        <div class="member-container">
            <!-- 页面标题 -->
            <div class="page-header">
                <div class="page-title-section">
                    <h1 class="page-title">消息中心</h1>
                    <p class="page-subtitle">
                        共 <?php echo count($messages); ?> 条消息
                        <?php if ($unreadCount > 0): ?>
                            ，<span class="unread-count"><?php echo $unreadCount; ?> 条未读</span>
                        <?php endif; ?>
                    </p>
                </div>
                <div class="page-actions">
                    <?php if ($unreadCount > 0): ?>
                        <button class="btn btn-outline" onclick="markAllAsRead()">
                            <i class="bi bi-check-all"></i>
                            <span>全部标记为已读</span>
                        </button>
                    <?php endif; ?>
                </div>
            </div>

            <!-- 消息筛选 -->
            <div class="message-filters">
                <button class="filter-btn active" data-type="all">
                    <i class="bi bi-inbox"></i>
                    <span>全部消息</span>
                </button>
                <button class="filter-btn" data-type="unread">
                    <i class="bi bi-envelope"></i>
                    <span>未读消息</span>
                    <?php if ($unreadCount > 0): ?>
                        <span class="badge"><?php echo $unreadCount; ?></span>
                    <?php endif; ?>
                </button>
                <button class="filter-btn" data-type="order">
                    <i class="bi bi-bag"></i>
                    <span>订单消息</span>
                </button>
                <button class="filter-btn" data-type="system">
                    <i class="bi bi-gear"></i>
                    <span>系统消息</span>
                </button>
                <button class="filter-btn" data-type="promotion">
                    <i class="bi bi-megaphone"></i>
                    <span>活动推广</span>
                </button>
            </div>

            <!-- 消息列表 -->
            <div class="messages-section">
                <?php if (empty($messages)): ?>
                    <div class="empty-state">
                        <div class="empty-icon">
                            <i class="bi bi-inbox"></i>
                        </div>
                        <h3>暂无消息</h3>
                        <p>您的消息将在这里显示</p>
                    </div>
                <?php else: ?>
                    <div class="messages-list">
                        <?php foreach ($messages as $message): ?>
                            <div class="message-item <?php echo !$message['is_read'] ? 'unread' : ''; ?>" 
                                 data-type="<?php echo $message['type']; ?>" 
                                 data-id="<?php echo $message['id']; ?>">
                                <div class="message-icon">
                                    <i class="bi <?php echo $message['icon']; ?>"></i>
                                </div>
                                <div class="message-content">
                                    <div class="message-header">
                                        <h3 class="message-title"><?php echo htmlspecialchars($message['title']); ?></h3>
                                        <div class="message-meta">
                                            <span class="message-time"><?php echo timeAgo($message['created_at']); ?></span>
                                            <?php if (!$message['is_read']): ?>
                                                <span class="unread-dot"></span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <p class="message-text"><?php echo htmlspecialchars($message['content']); ?></p>
                                </div>
                                <div class="message-actions">
                                    <?php if (!$message['is_read']): ?>
                                        <button class="action-btn mark-read-btn" onclick="markAsRead(<?php echo $message['id']; ?>)">
                                            <i class="bi bi-check"></i>
                                        </button>
                                    <?php endif; ?>
                                    <button class="action-btn delete-btn" onclick="deleteMessage(<?php echo $message['id']; ?>)">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</main>

<script>
// 消息筛选功能
document.addEventListener('DOMContentLoaded', function() {
    const filterBtns = document.querySelectorAll('.filter-btn');
    const messageItems = document.querySelectorAll('.message-item');
    
    filterBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const filterType = this.dataset.type;
            
            // 更新按钮状态
            filterBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            // 筛选消息
            messageItems.forEach(item => {
                if (filterType === 'all') {
                    item.style.display = 'flex';
                } else if (filterType === 'unread') {
                    item.style.display = item.classList.contains('unread') ? 'flex' : 'none';
                } else {
                    item.style.display = item.dataset.type === filterType ? 'flex' : 'none';
                }
            });
        });
    });
});

function markAsRead(messageId) {
    // 这里应该发送AJAX请求到服务器
    const messageItem = document.querySelector(`[data-id="${messageId}"]`);
    if (messageItem) {
        messageItem.classList.remove('unread');
        const unreadDot = messageItem.querySelector('.unread-dot');
        if (unreadDot) {
            unreadDot.remove();
        }
        const markReadBtn = messageItem.querySelector('.mark-read-btn');
        if (markReadBtn) {
            markReadBtn.remove();
        }
    }
    
    // 更新未读数量
    updateUnreadCount();
}

function markAllAsRead() {
    if (confirm('确定要将所有消息标记为已读吗？')) {
        // 这里应该发送AJAX请求到服务器
        document.querySelectorAll('.message-item.unread').forEach(item => {
            item.classList.remove('unread');
            const unreadDot = item.querySelector('.unread-dot');
            if (unreadDot) {
                unreadDot.remove();
            }
            const markReadBtn = item.querySelector('.mark-read-btn');
            if (markReadBtn) {
                markReadBtn.remove();
            }
        });
        
        // 更新页面
        updateUnreadCount();
        location.reload();
    }
}

function deleteMessage(messageId) {
    if (confirm('确定要删除这条消息吗？')) {
        // 这里应该发送AJAX请求到服务器
        const messageItem = document.querySelector(`[data-id="${messageId}"]`);
        if (messageItem) {
            messageItem.style.animation = 'fadeOut 0.3s ease-out';
            setTimeout(() => {
                messageItem.remove();
            }, 300);
        }
    }
}

function updateUnreadCount() {
    const unreadItems = document.querySelectorAll('.message-item.unread');
    const unreadCount = unreadItems.length;
    
    // 更新页面标题中的未读数量
    const subtitle = document.querySelector('.page-subtitle');
    if (subtitle) {
        const text = subtitle.textContent.split('，')[0];
        if (unreadCount > 0) {
            subtitle.innerHTML = text + '，<span class="unread-count">' + unreadCount + ' 条未读</span>';
        } else {
            subtitle.textContent = text;
        }
    }
    
    // 更新筛选按钮中的徽章
    const unreadFilterBtn = document.querySelector('[data-type="unread"]');
    const badge = unreadFilterBtn.querySelector('.badge');
    if (unreadCount > 0) {
        if (badge) {
            badge.textContent = unreadCount;
        } else {
            const newBadge = document.createElement('span');
            newBadge.className = 'badge';
            newBadge.textContent = unreadCount;
            unreadFilterBtn.appendChild(newBadge);
        }
    } else if (badge) {
        badge.remove();
    }
}
</script>

<?php require_once 'includes/footer.php'; ?>
