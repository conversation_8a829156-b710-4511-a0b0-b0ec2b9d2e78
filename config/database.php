<?php
// 数据库配置
define('DB_HOST', 'localhost');
define('DB_NAME', 'xianyu_db');
define('DB_USER', 'xianyu_db');
define('DB_PASS', '2CY9SsWpXs6yWHks');
define('DB_CHARSET', 'utf8mb4');

// 创建数据库连接
try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET,
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]
    );
} catch (PDOException $e) {
    die("数据库连接失败: " . $e->getMessage());
}

// 网站配置
define('SITE_NAME', '数字鱼');
define('SITE_URL', 'http://localhost/xianyu');
define('UPLOAD_PATH', 'uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB

// 会话配置
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
?>
